# Apartment Management System - Backend API

A comprehensive multi-tenant apartment management system built with NestJS, featuring role-based access control, utility billing, and building management capabilities.

## 🏗️ Technology Stack

- **Framework**: NestJS (Node.js)
- **Database**: PostgreSQL
- **ORM**: Prisma
- **Authentication**: JWT with Passport
- **Documentation**: Swagger/OpenAPI
- **Language**: TypeScript

## 📋 Prerequisites

Before setting up the backend, ensure you have the following installed:

### Required Software
- **Node.js**: Version 18.0.0 or higher
- **npm**: Version 8.0.0 or higher (comes with Node.js)
- **PostgreSQL**: Version 13.0 or higher
- **Git**: For version control

### Development Tools (Recommended)
- **VS Code**: With TypeScript and Prisma extensions
- **Postman** or **Insomnia**: For API testing
- **pgAdmin** or **DBeaver**: For database management

### System Requirements
- **RAM**: Minimum 4GB (8GB recommended)
- **Storage**: At least 1GB free space
- **OS**: Windows 10+, macOS 10.15+, or Linux

## 🚀 Step-by-Step Setup Instructions

### 1. Clone and Navigate to Backend

```bash
# Clone the repository (if not already done)
git clone <repository-url>
cd apartment-management-system/backend
```

### 2. Install Dependencies

```bash
# Install all required packages
npm install

# This will install:
# - NestJS framework and modules
# - Prisma ORM and client
# - Authentication packages (JWT, Passport)
# - Validation and transformation libraries
# - Development tools and type definitions
```

### 3. Database Setup

#### Option A: Local PostgreSQL Installation

1. **Install PostgreSQL** (if not already installed):
   - **Windows**: Download from [postgresql.org](https://www.postgresql.org/download/windows/)
   - **macOS**: Use Homebrew: `brew install postgresql`
   - **Linux**: `sudo apt-get install postgresql postgresql-contrib`

2. **Start PostgreSQL service**:
   ```bash
   # macOS (Homebrew)
   brew services start postgresql
   
   # Linux
   sudo systemctl start postgresql
   
   # Windows: Use Services app or pgAdmin
   ```

3. **Create database and user**:
   ```sql
   -- Connect to PostgreSQL as superuser
   psql -U postgres
   
   -- Create database
   CREATE DATABASE apartment_management;
   
   -- Create user (optional, for security)
   CREATE USER apartment_user WITH PASSWORD 'your_secure_password';
   GRANT ALL PRIVILEGES ON DATABASE apartment_management TO apartment_user;
   
   -- Exit psql
   \q
   ```

#### Option B: Docker PostgreSQL (Alternative)

```bash
# Run PostgreSQL in Docker container
docker run --name apartment-postgres \
  -e POSTGRES_DB=apartment_management \
  -e POSTGRES_USER=apartment_user \
  -e POSTGRES_PASSWORD=your_secure_password \
  -p 5432:5432 \
  -d postgres:15
```

### 4. Environment Configuration

Create a `.env` file in the backend root directory:

```bash
# Copy the example environment file
cp .env.example .env
```

Edit the `.env` file with your configuration:

```env
# Database Configuration
DATABASE_URL="postgresql://apartment_user:your_secure_password@localhost:5432/apartment_management?schema=public"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"

# Application Configuration
PORT=3001
NODE_ENV="development"

# CORS Configuration (for frontend connection)
FRONTEND_URL="http://localhost:3000"
```

**Important Security Notes:**
- Change `JWT_SECRET` to a strong, unique value in production
- Use environment-specific database credentials
- Never commit `.env` files to version control

### 5. Database Migration and Seeding

```bash
# Generate Prisma client
npx prisma generate

# Run database migrations (creates tables)
npx prisma migrate dev --name init

# Optional: Seed database with sample data
npx prisma db seed
```

**What this does:**
- Creates all necessary database tables (users, buildings, rooms, billing, etc.)
- Sets up relationships and constraints
- Generates TypeScript types for database models
- Optionally adds sample data for testing

### 6. Start the Development Server

```bash
# Start in development mode with hot reload
npm run start:dev

# Alternative: Start in regular mode
npm run start
```

**Expected Output:**
```
[Nest] Starting Nest application...
[Nest] BillingModule dependencies initialized
[Nest] AuthModule dependencies initialized
[Nest] Nest application successfully started
Application is running on: http://localhost:3001
Swagger documentation: http://localhost:3001/api
```

### 7. Verify Setup

1. **Health Check**: Visit `http://localhost:3001` - should return "Hello World!"

2. **API Documentation**: Visit `http://localhost:3001/api` - should show Swagger UI

3. **Database Connection**: Check logs for successful Prisma connection

4. **Test API Endpoint**:
   ```bash
   # Test user registration
   curl -X POST http://localhost:3001/auth/register \
     -H "Content-Type: application/json" \
     -d '{
       "email": "<EMAIL>",
       "username": "admin",
       "password": "password123"
     }'
   ```

## 📁 Project Structure Overview

```
backend/
├── src/
│   ├── auth/                 # Authentication module
│   │   ├── guards/          # JWT and role-based guards
│   │   ├── strategies/      # Passport strategies
│   │   └── decorators/      # Custom decorators
│   ├── billing/             # Billing and pricing module
│   │   ├── dto/            # Data transfer objects
│   │   └── entities/       # Database entities
│   ├── buildings/          # Building management module
│   ├── rooms/              # Room management module
│   ├── user-management/    # Multi-tenant user management
│   ├── prisma/             # Database configuration
│   │   ├── migrations/     # Database migrations
│   │   └── schema.prisma   # Database schema
│   └── main.ts             # Application entry point
├── test/                   # Test files
├── .env                    # Environment variables
├── package.json           # Dependencies and scripts
└── README.md              # This file
```

### Key Architecture Concepts

**Multi-Tenant Design**: Each building acts as a tenant with isolated data and user access.

**Role-Based Access Control (RBAC)**:
- **ADMIN**: Can create buildings and manage all aspects
- **USER**: Can only access assigned buildings and perform limited operations

**Guard-Based Security**: Uses NestJS guards for consistent authorization across all endpoints.

**Prisma ORM**: Provides type-safe database access with automatic migrations.

## ⚡ Quick Start

For experienced developers who want to get started immediately:

```bash
# 1. Clone and navigate
git clone <repository-url>
cd apartment-management-system/backend

# 2. Install dependencies
npm install

# 3. Setup environment
cp .env.example .env
# Edit .env with your database credentials

# 4. Setup database
npx prisma generate
npx prisma migrate dev --name init
npx prisma db seed

# 5. Start development server
npm run start:dev
```

**Verify setup**: Visit `http://localhost:3001/api` for Swagger documentation.

## 🏗️ Creating NestJS Project from Scratch

If you want to understand how this backend was created, here are the commands used:

### 1. Initial NestJS Setup
```bash
# Install NestJS CLI globally
npm install -g @nestjs/cli

# Create new NestJS project
nest new apartment-management-backend
cd apartment-management-backend

# Install additional dependencies
npm install @nestjs/passport @nestjs/jwt passport passport-local passport-jwt
npm install @nestjs/swagger swagger-ui-express
npm install @prisma/client prisma
npm install class-validator class-transformer
npm install bcryptjs
npm install @nestjs/config

# Install development dependencies
npm install -D @types/passport-local @types/passport-jwt @types/bcryptjs
```

### 2. Generate NestJS Modules and Components
```bash
# Generate authentication module
nest generate module auth
nest generate service auth
nest generate controller auth

# Generate buildings module
nest generate module buildings
nest generate service buildings
nest generate controller buildings

# Generate rooms module
nest generate module rooms
nest generate service rooms
nest generate controller rooms

# Generate billing module
nest generate module billing
nest generate service billing
nest generate controller billing

# Generate user management module
nest generate module user-management
nest generate service user-management
nest generate controller user-management

# Generate Prisma service
nest generate service prisma
```

### 3. Setup Prisma ORM
```bash
# Initialize Prisma
npx prisma init

# After creating schema.prisma, generate client
npx prisma generate

# Create initial migration
npx prisma migrate dev --name init

# Create seed file
touch prisma/seed.ts
```

### 4. Setup Authentication Guards and Strategies
```bash
# Create guards directory
mkdir src/auth/guards
mkdir src/auth/strategies
mkdir src/auth/decorators

# Guards and strategies are created manually as TypeScript files
```

### 5. Configure Swagger Documentation
```bash
# Swagger is configured in main.ts with decorators throughout controllers
```

## 📋 Essential Commands

### Development Commands
```bash
# Start development server with hot reload
npm run start:dev

# Start in production mode
npm run start:prod

# Build the application
npm run build

# Run tests
npm run test
npm run test:e2e
npm run test:cov
```

### Database Commands
```bash
# Generate Prisma client (run after schema changes)
npx prisma generate

# Create and apply new migration
npx prisma migrate dev --name <migration-name>

# Apply migrations to production database
npx prisma migrate deploy

# Reset database (development only - DESTRUCTIVE)
npx prisma migrate reset

# Seed database with sample data
npx prisma db seed

# Open Prisma Studio (database GUI)
npx prisma studio

# Pull schema from existing database
npx prisma db pull

# Push schema changes without migration
npx prisma db push
```

### Database Migration Workflow
```bash
# 1. Make changes to prisma/schema.prisma
# 2. Generate and apply migration
npx prisma migrate dev --name add_new_feature

# 3. Generate updated Prisma client
npx prisma generate

# 4. Restart your application
npm run start:dev
```

### Production Database Setup
```bash
# 1. Set production DATABASE_URL in .env
# 2. Deploy migrations to production
npx prisma migrate deploy

# 3. Generate Prisma client for production
npx prisma generate

# Note: Never use 'migrate dev' in production
```

### Useful Development Commands
```bash
# Format code
npm run format

# Lint code
npm run lint

# Type check
npm run type-check

# View all available scripts
npm run
```

## 🔧 Common Issues & Troubleshooting

### Database Connection Issues

**Problem**: `Error: P1001: Can't reach database server`
```bash
# Solution: Check if PostgreSQL is running
# macOS
brew services list | grep postgresql

# Linux
sudo systemctl status postgresql

# Windows: Check Services app
```

**Problem**: `Error: P3009: migrate found failed migration`
```bash
# Solution: Reset database (development only)
npx prisma migrate reset
npx prisma migrate dev --name init
```

### Port Conflicts

**Problem**: `Error: listen EADDRINUSE: address already in use :::3001`
```bash
# Solution 1: Kill process using port 3001
lsof -ti:3001 | xargs kill -9

# Solution 2: Change port in .env file
PORT=3002
```

### Environment Variables

**Problem**: JWT authentication not working
- **Solution**: Ensure `JWT_SECRET` is set in `.env` file
- **Check**: Verify `.env` file is in backend root directory
- **Restart**: Restart the server after changing environment variables

### Prisma Issues

**Problem**: `Error: Generator "client" could not be found`
```bash
# Solution: Reinstall Prisma
npm uninstall prisma @prisma/client
npm install prisma @prisma/client
npx prisma generate
```

**Problem**: Database schema out of sync
```bash
# Solution: Reset and regenerate
npx prisma db push --force-reset
npx prisma generate
```

## 🧪 Testing Instructions

### 1. Manual API Testing

Use the Swagger UI at `http://localhost:3001/api` or test with curl:

```bash
# 1. Register a new admin user
curl -X POST http://localhost:3001/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "username": "admin",
    "password": "password123"
  }'

# 2. Login to get JWT token
curl -X POST http://localhost:3001/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "admin",
    "password": "password123"
  }'

# 3. Create a building (use token from login response)
curl -X POST http://localhost:3001/buildings \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "Test Building"
  }'
```

### 2. Running Automated Tests

```bash
# Unit tests
npm run test

# End-to-end tests
npm run test:e2e

# Test coverage
npm run test:cov
```

### 3. Database Inspection

```bash
# Open Prisma Studio (database GUI)
npx prisma studio

# View database in browser at http://localhost:5555
```

## 🎯 Key Features to Test

### Authentication System
1. **User Registration**: Create ADMIN and USER accounts
2. **Login/Logout**: Test JWT token generation and validation
3. **Protected Routes**: Verify unauthorized access is blocked

### Multi-Tenant Building Management
1. **Create Buildings**: ADMIN users can create buildings
2. **Building Access**: USER accounts can only access assigned buildings
3. **Room Management**: Create and manage rooms within buildings

### Billing System
1. **Pricing Configuration**: Set water/electricity prices per building
2. **Billing Entries**: Create utility bills for rooms
3. **Access Control**: Verify USER role can create bills for assigned buildings

### User Management
1. **Create Users**: ADMIN can create USER accounts for buildings
2. **Building Assignment**: Assign users to specific buildings
3. **Role Restrictions**: Verify role-based access control

## 📚 Additional Resources

### Documentation
- [NestJS Documentation](https://docs.nestjs.com/)
- [Prisma Documentation](https://www.prisma.io/docs/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)

### API Reference
- **Swagger UI**: `http://localhost:3001/api` (when server is running)
- **Postman Collection**: Import from `/docs/postman-collection.json`

### Development Tools
- **Prisma Studio**: `npx prisma studio` - Database GUI
- **Database Migrations**: `npx prisma migrate dev`
- **Schema Introspection**: `npx prisma db pull`

### Next Steps for Development
1. **Add Unit Tests**: Expand test coverage for business logic
2. **Implement Caching**: Add Redis for session management
3. **Add Logging**: Implement structured logging with Winston
4. **API Versioning**: Add versioning strategy for API endpoints
5. **Rate Limiting**: Implement rate limiting for security
6. **File Upload**: Add support for document/image uploads

## 🚀 Production Deployment

### Environment Setup
```env
# Production environment variables
NODE_ENV="production"
DATABASE_URL="*********************************************/apartment_management"
JWT_SECRET="super-secure-production-secret"
PORT=3001
```

### Build and Start
```bash
# Build the application
npm run build

# Start in production mode
npm run start:prod
```

### Health Monitoring
- **Health Check Endpoint**: `GET /health`
- **Metrics**: Available at `/metrics` (if enabled)
- **Logs**: Configure log aggregation for production monitoring

---

## 📞 Support

If you encounter issues not covered in this guide:

1. **Check the logs**: Look for error messages in the console output
2. **Verify prerequisites**: Ensure all required software is installed and running
3. **Database connectivity**: Test PostgreSQL connection independently
4. **Environment variables**: Double-check all `.env` file configurations
5. **Port availability**: Ensure ports 3001 and 5432 are available

For additional help, refer to the official documentation links provided above.
