// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(uuid())
  email     String   @unique
  username  String   @unique
  password  String
  role      Role     @default(USER)
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  buildings       Building[]
  buildingPricing BuildingPricing[]
  buildingAccess  UserBuildingAccess[]
  paymentMethods  PaymentMethod[]

  @@map("users")
}

model Building {
  id        String   @id @default(uuid())
  name      String
  createdBy String   @map("created_by")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  user         User                 @relation(fields: [createdBy], references: [id], onDelete: Cascade)
  rooms        Room[]
  pricing      BuildingPricing?
  userAccess   UserBuildingAccess[]
  Customer     Customer[]
  BillingCycle BillingCycle[]

  @@map("buildings")
}

model Room {
  id                        String     @id @default(uuid())
  buildingId                String     @map("building_id")
  roomNumber                String     @map("room_number")
  monthlyRent               Decimal    @default(0) @map("monthly_rent") @db.Decimal(10, 2)
  initialWaterReading       Decimal    @default(0) @map("initial_water_reading") @db.Decimal(10, 2)
  initialElectricityReading Decimal    @default(0) @map("initial_electricity_reading") @db.Decimal(10, 2)
  currentWaterReading       Decimal    @default(0) @map("current_water_reading") @db.Decimal(10, 2)
  currentElectricityReading Decimal    @default(0) @map("current_electricity_reading") @db.Decimal(10, 2)
  status                    RoomStatus @default(AVAILABLE)
  createdAt                 DateTime   @default(now()) @map("created_at")
  updatedAt                 DateTime   @updatedAt @map("updated_at")

  // Relations
  building               Building                 @relation(fields: [buildingId], references: [id], onDelete: Cascade)
  additionalServices     AdditionalService[]
  billingEntries         BillingEntry[]
  CustomerRoomAssignment CustomerRoomAssignment[]

  @@unique([buildingId, roomNumber])
  @@map("rooms")
}

model AdditionalService {
  id          String   @id @default(uuid())
  roomId      String   @map("room_id")
  name        String
  price       Decimal  @default(0) @db.Decimal(10, 2)
  description String?
  createdAt   DateTime @default(now()) @map("created_at")

  // Relations
  room Room @relation(fields: [roomId], references: [id], onDelete: Cascade)

  @@map("additional_services")
}

model BillingCycle {
  id         String             @id @default(uuid())
  buildingId String             @map("building_id")
  startDate  DateTime           @default(now()) @map("start_date")
  endDate    DateTime           @default(now()) @map("end_date")
  updatedAt  DateTime           @updatedAt @map("updated_at")
  createdBy  String             @map("created_by")
  updatedBy  String             @map("updated_by")
  status     BillingCycleStatus @default(OPEN)

  // Relations
  building Building @relation(fields: [buildingId], references: [id], onDelete: Cascade)

  @@map("billing_cycles")
}

model BillingEntry {
  id                        String        @id @default(uuid())
  roomId                    String        @map("room_id")
  billingDate               DateTime      @map("billing_date") @db.Date
  monthlyRent               Decimal       @default(0) @map("monthly_rent") @db.Decimal(10, 2)
  initialWaterReading       Decimal       @map("initial_water_reading") @db.Decimal(10, 2)
  initialElectricityReading Decimal       @map("initial_electricity_reading") @db.Decimal(10, 2)
  waterReading              Decimal       @map("water_reading") @db.Decimal(10, 2)
  electricityReading        Decimal       @map("electricity_reading") @db.Decimal(10, 2)
  waterConsumption          Decimal       @map("water_consumption") @db.Decimal(10, 2)
  electricityConsumption    Decimal       @map("electricity_consumption") @db.Decimal(10, 2)
  waterCost                 Decimal       @map("water_cost") @db.Decimal(10, 2)
  electricityCost           Decimal       @map("electricity_cost") @db.Decimal(10, 2)
  additionalServicesCost    Decimal       @default(0) @map("additional_services_cost") @db.Decimal(10, 2)
  totalAmount               Decimal       @map("total_amount") @db.Decimal(10, 2)
  createdBy                 String        @map("created_by")
  createdAt                 DateTime      @default(now()) @map("created_at")
  status                    BillingStatus @default(UNPAID)

  // Relations
  room               Room                @relation(fields: [roomId], references: [id], onDelete: Cascade)
  billPaymentEntries BillPaymentEntriy[]

  @@map("billing_entries")
}

model BillPaymentEntriy {
  id                String   @id @default(uuid())
  billingId         String   @map("billing_id")
  amount            Decimal  @map("amount") @db.Decimal(10, 2)
  paymentDate       DateTime @map("payment_date") @db.Date
  paymentMethodName String   @default("Cash") @map("payment_method_name")
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")

  // Relations
  billingEntry BillingEntry @relation(fields: [billingId], references: [id], onDelete: Cascade)

  @@unique([id, billingId])
  @@map("bill_payment_entries")
}

model BuildingPricing {
  id                      String   @id @default(uuid())
  buildingId              String   @map("building_id")
  waterPricePerUnit       Decimal  @default(0) @map("water_price_per_unit") @db.Decimal(10, 4)
  electricityPricePerUnit Decimal  @default(0) @map("electricity_price_per_unit") @db.Decimal(10, 4)
  billingEnabled          Boolean  @default(false) @map("billing_enabled")
  updatedBy               String   @map("updated_by")
  updatedAt               DateTime @updatedAt @map("updated_at")

  // Relations
  building Building @relation(fields: [buildingId], references: [id], onDelete: Cascade)
  user     User     @relation(fields: [updatedBy], references: [id], onDelete: Cascade)

  @@unique([buildingId])
  @@map("building_pricing")
}

model UserBuildingAccess {
  id         String   @id @default(uuid())
  userId     String   @map("user_id")
  buildingId String   @map("building_id")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  // Relations
  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  building Building @relation(fields: [buildingId], references: [id], onDelete: Cascade)

  @@unique([userId, buildingId])
  @@map("user_building_access")
}

enum Role {
  ADMIN
  USER

  @@map("role")
}

enum RoomStatus {
  AVAILABLE
  OCCUPIED
  MAINTENANCE

  @@map("room_status")
}

enum BillingStatus {
  UNPAID
  PAID
  PARTIALLY_PAID

  @@map("billing_status")
}

enum BillingCycleStatus {
  OPEN
  CLOSED

  @@map("billing_cycle_status")
}

model PaymentMethod {
  id          String   @id @default(uuid())
  name        String
  description String?
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  createdBy   String   @map("created_by")

  // Relations
  user User @relation(fields: [createdBy], references: [id], onDelete: Cascade)

  // Unique constraint per user
  @@unique([name, createdBy])
  @@map("payment_methods")
}

model Customer {
  id          String   @id @default(uuid())
  nameEn      String   @map("name_en")
  nameTh      String?  @map("name_th")
  idNumber    String   @map("id_number")
  phoneNumber String   @map("phone_number")
  address     String?
  buildingId  String   @map("building_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  building        Building                 @relation(fields: [buildingId], references: [id], onDelete: Cascade)
  roomAssignments CustomerRoomAssignment[]

  // Unique constraint: ID number per building
  @@unique([idNumber, buildingId])
  @@map("customers")
}

model CustomerRoomAssignment {
  id         String   @id @default(uuid())
  customerId String   @map("customer_id")
  roomId     String   @map("room_id")
  assignedAt DateTime @default(now()) @map("assigned_at")
  createdAt  DateTime @default(now()) @map("created_at")

  // Relations
  customer Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)
  room     Room     @relation(fields: [roomId], references: [id], onDelete: Cascade)

  // Unique constraint: one assignment per customer-room pair
  @@unique([customerId, roomId])
  @@map("customer_room_assignments")
}
