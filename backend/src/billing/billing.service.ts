import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateBillingDto } from './dto/create-billing.dto';
import { UpdateBuildingPricingDto } from './dto/update-building-pricing.dto';
import { Prisma } from '@prisma/client';
import { TimezoneService } from '../common/timezone.service';

@Injectable()
export class BillingService {
  constructor(
    private prisma: PrismaService,
    private timezoneService: TimezoneService,
  ) {}

  // Helper method to check room access
  async checkRoomAccess(roomId: string, userId: string, userRole: string) {
    const room = await this.prisma.room.findUnique({
      where: { id: roomId },
      include: {
        building: {
          select: {
            id: true,
            createdBy: true,
          },
        },
      },
    });

    if (!room) {
      return null;
    }

    // Admin users have access to rooms in buildings they created
    if (userRole === 'ADMIN') {
      return room.building.createdBy === userId ? room : null;
    }

    // USER role needs explicit building access
    if (userRole === 'USER') {
      const access = await this.prisma.userBuildingAccess.findUnique({
        where: {
          userId_buildingId: {
            userId,
            buildingId: room.building.id,
          },
        },
      });

      return access ? room : null;
    }

    return null;
  }

  async createBillingEntry(
    roomId: string,
    createBillingDto: CreateBillingDto,
    userId: string,
  ) {
    // Check if room exists and user has access
    const room = await this.prisma.room.findUnique({
      where: { id: roomId },
      include: {
        building: {
          select: {
            id: true,
            name: true,
            createdBy: true,
          },
        },
        additionalServices: true,
      },
    });

    if (!room) {
      throw new NotFoundException('Room not found');
    }

    // Get building pricing
    const buildingPricing = await this.prisma.buildingPricing.findUnique({
      where: { buildingId: room.building.id },
    });

    if (!buildingPricing || !buildingPricing.billingEnabled) {
      throw new BadRequestException(
        'Billing is not enabled or building pricing not configured',
      );
    }

    const billingCalculation = this.calculateBillingAmounts(
      createBillingDto,
      room,
      buildingPricing,
    );

    // Create billing entry
    const billingEntry = await this.prisma.billingEntry.create({
      data: {
        roomId,
        billingDate: this.timezoneService.parseToUTC7(
          createBillingDto.billingDate,
        ),
        initialWaterReading: createBillingDto.initialWaterReading,
        initialElectricityReading: createBillingDto.initialElectricityReading,
        waterReading: createBillingDto.waterReading,
        electricityReading: createBillingDto.electricityReading,
        waterConsumption: billingCalculation.waterConsumption,
        electricityConsumption: billingCalculation.electricityConsumption,
        waterCost: billingCalculation.waterCost,
        electricityCost: billingCalculation.electricityCost,
        additionalServicesCost: billingCalculation.additionalServicesCost,
        totalAmount: billingCalculation.totalAmount,
        createdBy: userId,
        createdAt: this.timezoneService.getCurrentDateUTC7(),
      },
    });

    // Update room's current readings
    await this.prisma.room.update({
      where: { id: roomId },
      data: {
        initialWaterReading: createBillingDto.initialWaterReading,
        initialElectricityReading: createBillingDto.initialElectricityReading,
        currentWaterReading: createBillingDto.waterReading,
        currentElectricityReading: createBillingDto.electricityReading,
        updatedAt: this.timezoneService.getCurrentDateUTC7(),
      },
    });

    return billingEntry;
  }

  async findBillingEntries(roomId?: string) {
    if (roomId == null) {
      return [];
    }

    return this.prisma.billingEntry.findMany({
      where: { roomId },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async getBuildingPricing(buildingId: string) {
    // Check if building exists and user has access
    const building = await this.prisma.building.findUnique({
      where: { id: buildingId },
      select: {
        id: true,
        name: true,
        createdBy: true,
      },
    });

    if (!building) {
      throw new NotFoundException('Building not found');
    }

    const pricing = await this.prisma.buildingPricing.findUnique({
      where: { buildingId },
    });

    return (
      pricing || {
        id: null,
        buildingId,
        waterPricePerUnit: 0,
        electricityPricePerUnit: 0,
        billingEnabled: false,
        updatedBy: null,
        updatedAt: null,
      }
    );
  }

  async updateBuildingPricing(
    buildingId: string,
    updateBuildingPricingDto: UpdateBuildingPricingDto,
    userId: string,
    userRole: string,
  ) {
    // Check if building exists and user has access
    const building = await this.prisma.building.findUnique({
      where: { id: buildingId },
      select: {
        id: true,
        name: true,
        createdBy: true,
      },
    });

    if (!building) {
      throw new NotFoundException('Building not found');
    }

    if (userRole !== 'ADMIN' && building.createdBy !== userId) {
      throw new ForbiddenException('Access denied');
    }

    // Get current pricing or prepare default values
    const currentPricing = await this.prisma.buildingPricing.findUnique({
      where: { buildingId },
    });

    // Validate billing operations
    await this.validateBillingToggleOffIfNeeded(
      updateBuildingPricingDto,
      currentPricing,
      buildingId,
    );
    this.validatePricingChangesWhileBillingEnabled(
      updateBuildingPricingDto,
      currentPricing,
    );

    await this.handleBillingCycle(updateBuildingPricingDto, buildingId, userId);

    const data = {
      buildingId,
      waterPricePerUnit:
        updateBuildingPricingDto.waterPricePerUnit ??
        currentPricing?.waterPricePerUnit ??
        0,
      electricityPricePerUnit:
        updateBuildingPricingDto.electricityPricePerUnit ??
        currentPricing?.electricityPricePerUnit ??
        0,
      billingEnabled:
        updateBuildingPricingDto.billingEnabled ??
        currentPricing?.billingEnabled ??
        false,
      updatedBy: userId,
      updatedAt: this.timezoneService.getCurrentDateUTC7(),
    };

    return this.prisma.buildingPricing.upsert({
      where: { buildingId },
      update: data,
      create: data,
      include: {
        user: {
          select: {
            id: true,
            email: true,
          },
        },
        building: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });
  }

  private async handleBillingCycle(
    updateBuildingPricingDto: UpdateBuildingPricingDto,
    buildingId: string,
    userId: string,
  ) {
    if (
      updateBuildingPricingDto.isBillingEntryToggled &&
      updateBuildingPricingDto.billingEnabled
    ) {
      await this.createNewBillingCycle(buildingId, userId);
    }
    if (
      updateBuildingPricingDto.isBillingEntryToggled &&
      !updateBuildingPricingDto.billingEnabled
    ) {
      await this.closeCurrentBillingCycle(buildingId, userId);
    }
  }

  private async createNewBillingCycle(buildingId: string, userId: string) {
    try {
      await this.prisma.billingCycle.create({
        data: {
          buildingId,
          startDate: this.timezoneService.getCurrentDateUTC7(),
          endDate: this.timezoneService.getCurrentDateUTC7(),
          updatedAt: this.timezoneService.getCurrentDateUTC7(),
          createdBy: userId,
          updatedBy: userId,
          status: 'OPEN',
        },
      });
    } catch (error) {
      console.error('Error creating new billing cycle:', error);
      throw new Error('Failed to create new billing cycle');
    }
  }

  private async closeCurrentBillingCycle(buildingId: string, userId: string) {
    try {
      const currentBillingCycle = await this.prisma.billingCycle.findFirst({
        where: { buildingId, status: 'OPEN' },
        orderBy: { startDate: 'desc' },
      });

      if (!currentBillingCycle) {
        throw new Error('No open billing cycle found');
      }

      await this.prisma.billingCycle.update({
        where: { id: currentBillingCycle.id },
        data: {
          endDate: this.timezoneService.getCurrentDateUTC7(),
          updatedAt: this.timezoneService.getCurrentDateUTC7(),
          updatedBy: userId,
          status: 'CLOSED',
        },
      });
    } catch (error) {
      console.error('Error closing current billing cycle:', error);
      throw new Error('Failed to close current billing cycle');
    }
  }

  //get history by month
  async getBillingHistoryByMonth(
    buildingId: string,
    month: string,
    year: string,
  ) {
    const { startDate, endDate } = this.timezoneService.getMonthRange(
      Number(year),
      Number(month),
    );

    const billEntries = await this.prisma.$queryRaw<
      Array<{
        id: string;
        room_id: string;
        created_at: Date;
        initial_water_reading: number;
        initial_electricity_reading: number;
        electricity_reading: number;
        water_reading: number;
        water_consumption: number;
        electricity_consumption: number;
        total_amount: number;
        status: string;
        monthly_rent: number;
        room_number: string;
      }>
    >(
      Prisma.sql`
        SELECT
          tmp.id,
          tmp.room_id,
          tmp.created_at,
          tmp.initial_water_reading,
          tmp.initial_electricity_reading,
          tmp.electricity_reading,
          tmp.water_reading,
          tmp.water_consumption,
          tmp.electricity_consumption,
          tmp.total_amount,
          tmp.status,
          tmp.monthly_rent,
          tmp.room_number
        FROM (
          SELECT
           be.id,
           be.room_id,
           be.created_at,
           be.initial_water_reading,
           be.initial_electricity_reading,
           be.electricity_reading,
           be.water_reading,
           be.water_consumption,
           be.electricity_consumption,
           be.total_amount,
           be.status,
           be.monthly_rent,
           r.room_number,
           ROW_NUMBER() OVER (PARTITION BY be.room_id ORDER BY be.created_at DESC) AS rn
          FROM rooms r
          LEFT JOIN billing_entries be ON r.id = be.room_id
          WHERE be.billing_date BETWEEN ${startDate} AND ${endDate}
          and r.building_id  = ${buildingId}
        ) AS tmp
        WHERE tmp.rn = 1
        ORDER BY tmp.created_at DESC;
      `,
    );

    // Process each entry to calculate overdue amounts
    const processedEntries = await Promise.all(
      billEntries.map(async (entry) => {
        let overDueAmount = 0;

        if (entry.status === 'PARTIALLY_PAID') {
          const paymentSum = await this.prisma.billPaymentEntriy.aggregate({
            where: { billingId: entry.id },
            _sum: {
              amount: true,
            },
          });

          overDueAmount =
            entry.total_amount - (paymentSum._sum.amount?.toNumber() || 0);
        }

        return {
          ...entry,
          overDueAmount,
        };
      }),
    );

    return processedEntries;
  }

  async addPaymentEntry(
    billingId: string,
    amount: number,
    paymentDate: Date,
    paymentMethodName: string,
  ) {
    if (this.timezoneService.isFutureDate(paymentDate)) {
      throw new BadRequestException('Payment date cannot be in the future');
    }

    // Get billing entry to validate it exists and get total amount
    const billingEntry = await this.prisma.billingEntry.findUnique({
      where: { id: billingId },
    });

    if (!billingEntry) {
      throw new NotFoundException('Billing entry not found');
    }

    // Get existing payments to calculate remaining balance
    const existingPayments = await this.prisma.billPaymentEntriy.aggregate({
      where: { billingId },
      _sum: { amount: true },
    });

    const totalPaid = existingPayments._sum.amount?.toNumber() || 0;
    const totalAmount = billingEntry.totalAmount.toNumber();
    const remainingBalance = totalAmount - totalPaid;

    // Validate payment amount
    if (amount <= 0) {
      throw new BadRequestException('Payment amount must be positive');
    }

    if (amount > remainingBalance) {
      throw new BadRequestException(
        `Payment amount (${amount}) exceeds remaining balance (${remainingBalance})`,
      );
    }

    // Use transaction to ensure atomicity
    return this.prisma.$transaction(async (prisma) => {
      // Create payment entry
      const paymentEntry = await prisma.billPaymentEntriy.create({
        data: {
          billingId,
          amount,
          paymentDate: paymentDate,
          paymentMethodName,
        },
      });

      // Calculate new total paid amount
      const newTotalPaid = totalPaid + amount;

      // Update billing entry status
      let newStatus: 'PAID' | 'PARTIALLY_PAID' | 'UNPAID' = 'UNPAID';
      if (newTotalPaid >= totalAmount) {
        newStatus = 'PAID';
      } else if (newTotalPaid > 0) {
        newStatus = 'PARTIALLY_PAID';
      }

      await prisma.billingEntry.update({
        where: { id: billingId },
        data: { status: newStatus },
      });

      return paymentEntry;
    });
  }

  async getPaymentHistory(billingId: string) {
    // Verify billing entry exists
    const billingEntry = await this.prisma.billingEntry.findUnique({
      where: { id: billingId },
    });

    if (!billingEntry) {
      throw new NotFoundException('Billing entry not found');
    }

    return this.prisma.billPaymentEntriy.findMany({
      where: { billingId },
      orderBy: { paymentDate: 'desc' },
    });
  }

  /**
   * Helper method to analyze room billing status for a building
   */
  private async analyzeRoomBillingStatus(
    buildingId: string,
    billingEnabledDate: Date,
  ): Promise<{
    roomsWithoutBilling: string[];
    roomsWithOldBilling: string[];
  }> {
    const rooms = await this.prisma.room.findMany({
      where: { buildingId, status: 'OCCUPIED' },
      select: {
        id: true,
        roomNumber: true,
        billingEntries: {
          where: {
            createdAt: {
              gte: billingEnabledDate,
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
          take: 1,
        },
      },
    });

    const roomsWithoutBilling: string[] = [];
    const roomsWithOldBilling: string[] = [];

    for (const room of rooms) {
      if (room.billingEntries.length === 0) {
        roomsWithoutBilling.push(room.roomNumber);
      } else {
        const mostRecentEntry = room.billingEntries[0];

        if (mostRecentEntry.createdAt < billingEnabledDate) {
          roomsWithOldBilling.push(room.roomNumber);
        }
      }
    }

    return { roomsWithoutBilling, roomsWithOldBilling };
  }

  /**
   * Validate billing toggle off if needed
   */
  private async validateBillingToggleOffIfNeeded(
    updateBuildingPricingDto: UpdateBuildingPricingDto,
    currentPricing: any,
    buildingId: string,
  ): Promise<void> {
    // If trying to turn OFF billing, validate conditions
    if (
      updateBuildingPricingDto.billingEnabled === false &&
      currentPricing?.billingEnabled === true
    ) {
      await this.validateBillingToggleOff(
        buildingId,
        new Date(updateBuildingPricingDto.billingEnabledDate),
      );
    }
  }

  /**
   * Validate pricing changes while billing is enabled
   */
  private validatePricingChangesWhileBillingEnabled(
    updateBuildingPricingDto: UpdateBuildingPricingDto,
    currentPricing: any,
  ): void {
    if (
      currentPricing?.billingEnabled === true &&
      (updateBuildingPricingDto?.electricityPricePerUnit !==
        currentPricing?.electricityPricePerUnit?.toNumber() ||
        updateBuildingPricingDto?.waterPricePerUnit !==
          currentPricing?.waterPricePerUnit?.toNumber())
    ) {
      throw new BadRequestException(
        'Cannot change pricing while billing is enabled. Please disable billing first.',
      );
    }
  }

  /**
   * Validate if billing toggle can be turned OFF
   * Checks that all rooms have billing entries created after the billing-enabled date
   */
  private async validateBillingToggleOff(
    buildingId: string,
    billingEnabledDate: Date,
  ): Promise<void> {
    const { roomsWithoutBilling, roomsWithOldBilling } =
      await this.analyzeRoomBillingStatus(buildingId, billingEnabledDate);

    // Build error message if validation fails
    const errors: string[] = [];

    if (roomsWithoutBilling.length > 0) {
      errors.push(
        `The following rooms have no billing entries: ${roomsWithoutBilling.join(', ')}`,
      );
    }

    if (roomsWithOldBilling.length > 0) {
      errors.push(
        `The following rooms have billing entries that were created before billing was enabled (${billingEnabledDate.toISOString().split('T')[0]}): ${roomsWithOldBilling.join(', ')}`,
      );
    }

    if (errors.length > 0) {
      throw new BadRequestException(
        `Cannot disable billing. ${errors.join('. ')}. Please ensure all rooms have billing entries created after ${billingEnabledDate.toISOString().split('T')[0]}.`,
      );
    }
  }

  /**
   * Get billing toggle validation status for a building
   */
  async getBillingToggleValidation(buildingId: string) {
    const billingCycle = await this.prisma.billingCycle.findFirst({
      where: { buildingId, status: 'OPEN' },
      orderBy: { startDate: 'desc' },
    });
    if (!billingCycle) {
      return {
        canToggleOff: true,
        billingEnabledDate: null,
        roomsWithoutBilling: [],
        roomsWithOldBilling: [],
        message: 'No open billing cycle found',
      };
    }

    // Use the helper method to analyze room billing status
    const { roomsWithoutBilling, roomsWithOldBilling } =
      await this.analyzeRoomBillingStatus(buildingId, billingCycle.startDate);

    const canToggleOff =
      roomsWithoutBilling.length === 0 && roomsWithOldBilling.length === 0;

    return {
      canToggleOff,
      billingEnabledDate: billingCycle.startDate,
      roomsWithoutBilling,
      roomsWithOldBilling,
      message: canToggleOff
        ? 'All rooms have valid billing entries. Billing can be disabled.'
        : 'Some rooms are missing billing entries or have outdated entries.',
    };
  }

  /**
   * Private method to calculate billing amounts
   */
  private calculateBillingAmounts(
    createBillingDto: CreateBillingDto,
    room: any,
    buildingPricing: any,
  ): {
    waterConsumption: number;
    electricityConsumption: number;
    waterCost: number;
    electricityCost: number;
    additionalServicesCost: number;
    totalAmount: number;
  } {
    // Calculate consumption
    const waterConsumption = Math.max(
      0,
      createBillingDto.waterReading - room.currentWaterReading.toNumber(),
    );
    const electricityConsumption = Math.max(
      0,
      createBillingDto.electricityReading -
        room.currentElectricityReading.toNumber(),
    );

    // Calculate costs
    const waterCost =
      waterConsumption * buildingPricing.waterPricePerUnit.toNumber();
    const electricityCost =
      electricityConsumption *
      buildingPricing.electricityPricePerUnit.toNumber();
    const additionalServicesCost = room.additionalServices.reduce(
      (sum: number, service: any) => sum + service.price.toNumber(),
      0,
    );
    const totalAmount =
      room.monthlyRent.toNumber() +
      waterCost +
      electricityCost +
      additionalServicesCost;

    return {
      waterConsumption,
      electricityConsumption,
      waterCost,
      electricityCost,
      additionalServicesCost,
      totalAmount,
    };
  }

  async getCurrentBillingCycle(buildingId: string) {
    const currentBillingCycle = await this.prisma.billingCycle.findFirst({
      where: { buildingId, status: 'OPEN' },
      orderBy: { startDate: 'desc' },
    });

    return currentBillingCycle;
  }
}
