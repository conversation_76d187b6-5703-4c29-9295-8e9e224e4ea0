import { <PERSON>N<PERSON><PERSON>, <PERSON><PERSON>ate<PERSON>tring, Min } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class CreateBillingDto {
  @ApiProperty({ example: '2024-01-15' })
  @IsDateString()
  billingDate: string;

  @ApiProperty({ example: 140.5 })
  @IsNumber()
  @Min(0)
  @Transform(({ value }) => parseFloat(value))
  initialWaterReading: number;

  @ApiProperty({ example: 290.75 })
  @IsNumber()
  @Min(0)
  @Transform(({ value }) => parseFloat(value))
  initialElectricityReading: number;

  @ApiProperty({ example: 150.5 })
  @IsNumber()
  @Min(0)
  @Transform(({ value }) => parseFloat(value))
  waterReading: number;

  @ApiProperty({ example: 300.75 })
  @IsNumber()
  @Min(0)
  @Transform(({ value }) => parseFloat(value))
  electricityReading: number;
}
