"use client";

import { useState, useEffect, useCallback } from "react";
import { useAuth } from "@/lib/auth";
import { BillingHistoryEntry, BillingPdf, Building, RoomPdf } from "@/types";
import { processBillingHistoryEntry, formatDate } from "@/lib/utils";
import PaymentModal from "@/components/PaymentModal";
import { useParams, useRouter } from "next/navigation";
import { buildingService } from "@/lib/services/buildings";
import Button from "@/components/ui/Button";
import { ArrowLeft } from "lucide-react";
import { billingService } from "@/lib/services/billing";
import { roomService } from "@/lib/services/rooms";
import { customersService } from "@/lib/services/customers";
import { PdfService } from "@/lib/services/pdf";
import { Download, FileText } from "lucide-react";

export default function BillingHistoryPage() {
  const { user, isAdmin, signOut } = useAuth();
  const router = useRouter();
  const params = useParams();
  const buildingId = params.id as string;

  const [building, setBuilding] = useState<Building | null>(null);
  const [billingHistory, setBillingHistory] = useState<BillingHistoryEntry[]>(
    []
  );
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [filterMonth, setFilterMonth] = useState(new Date().getMonth() + 1);
  const [filterYear, setFilterYear] = useState(new Date().getFullYear());
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [readOnly, setReadOnly] = useState(false);
  const [selectedBillingEntry, setSelectedBillingEntry] =
    useState<BillingHistoryEntry | null>(null);

  // PDF generation states
  const [isGeneratingPdf, setIsGeneratingPdf] = useState(false);
  const [isBulkDownloading, setIsBulkDownloading] = useState(false);
  const [bulkProgress, setBulkProgress] = useState({ current: 0, total: 0 });

  const fetchBillingHistory = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const { data, error } = await billingService.getBillingHistory(
        buildingId,
        filterMonth,
        filterYear
      );

      if (error) {
        setError(error);
      } else if (data) {
        // Convert Decimal fields to numbers for frontend consumption
        const processedData = data.map(processBillingHistoryEntry);
        setBillingHistory(processedData);
      }
    } catch (err: any) {
      setError(
        err.response?.data?.message || "Failed to fetch billing history"
      );
    } finally {
      setLoading(false);
    }
  }, [filterMonth, filterYear]);

  const handleFilter = () => {
    setFilterMonth(selectedMonth);
    setFilterYear(selectedYear);
  };

  useEffect(() => {
    // Load initial data on component mount
    fetchBuilding();
    fetchBillingHistory();
  }, [fetchBillingHistory]);

  const fetchBuilding = async () => {
    setLoading(true);
    const { data, error } = await buildingService.getBuildingById(buildingId);

    if (error) {
      setError(error);
    } else if (data) {
      setBuilding(data);
    }

    setLoading(false);
  };

  const handleShowPaymentManagement = (entry: BillingHistoryEntry) => {
    setSelectedBillingEntry(entry);
    setShowPaymentModal(true);
    setReadOnly(true);
  };

  const handleAddPayment = (entry: BillingHistoryEntry) => {
    setSelectedBillingEntry(entry);
    setShowPaymentModal(true);
  };

  const handlePaymentSuccess = () => {
    setShowPaymentModal(false);
    setSelectedBillingEntry(null);
    fetchBillingHistory(); // Refresh the data
  };

  const getStatusBadge = (status: string) => {
    const baseClasses =
      "px-3 py-1 rounded-full text-xs font-semibold uppercase tracking-wide";
    switch (status) {
      case "PAID":
        return `${baseClasses} bg-green-100 text-green-800 border border-green-200`;
      case "UNPAID":
        return `${baseClasses} bg-red-100 text-red-800 border border-red-200`;
      case "PARTIALLY_PAID":
        return `${baseClasses} bg-yellow-100 text-yellow-800 border border-yellow-200`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800 border border-gray-200`;
    }
  };

  const months = [
    { value: 1, label: "January" },
    { value: 2, label: "February" },
    { value: 3, label: "March" },
    { value: 4, label: "April" },
    { value: 5, label: "May" },
    { value: 6, label: "June" },
    { value: 7, label: "July" },
    { value: 8, label: "August" },
    { value: 9, label: "September" },
    { value: 10, label: "October" },
    { value: 11, label: "November" },
    { value: 12, label: "December" },
  ];

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 10 }, (_, i) => currentYear - 5 + i);

  const getRoomAdditionalServices = async (roomId: string) => {
    const { data, error } = await roomService.findAdditionalServices(roomId);

    if (error) {
      setError(error);
    } else if (data) {
      return data;
    }
  };

  const fetchBuildingPricing = async () => {
    if (!buildingId) return;

    const { data, error } = await billingService.getBuildingPricing(buildingId);

    if (error) {
      setError(error);
    } else if (data) {
      return data;
    }
  };

  const getCustomerByRoom = async (roomId: string) => {
    const { data, error } = await customersService.getCustomersByRoomId(roomId);

    if (error) {
      setError(error);
    } else if (data) {
      return data;
    }
  };

  const prepareRoomForPDF = async (entry: BillingHistoryEntry) => {
    const additionalServices = await getRoomAdditionalServices(entry.room_id);
    console.log("Additional services:", additionalServices);
    const buildingPricing = await fetchBuildingPricing();
    const customers = await getCustomerByRoom(entry.room_id);

    const customer =
      customers && customers.length > 0
        ? customers?.sort((a, b) =>
            a.customer!.nameEn.localeCompare(b.customer!.nameEn)
          )[0].customer
        : null;

    const room: RoomPdf = {
      buildingName: building?.name || "",
      customerName: customer?.nameEn || "Unknown Customer",
      roomNumber: entry.room_number,
      monthlyRent: entry.monthly_rent,
      billingEnabledDate: entry.created_at, //ผิด
      initialWaterReading: entry.initial_water_reading,
      initialElectricityReading: entry.initial_electricity_reading,
      currentWaterReading: entry.water_reading,
      currentElectricityReading: entry.electricity_reading,
      waterPricePerUnit: Number(buildingPricing?.waterPricePerUnit || 0),
      electricityPricePerUnit: Number(
        buildingPricing?.electricityPricePerUnit || 0
      ),
      waterConsumption: entry.water_consumption,
      electricityConsumption: entry.electricity_consumption,
      additionalServices: additionalServices || [],
    };

    return room;
  };

  const handlePrint = async (entry: BillingHistoryEntry) => {
    if (!building) return;

    try {
      setIsGeneratingPdf(true);

      const room = await prepareRoomForPDF(entry);
      const billingPdf = preparePdfData(room);

      // Validate the data before passing to PDF service
      if (!billingPdf.buildingName || !billingPdf.roomNumber) {
        throw new Error("Missing required billing data");
      }

      PdfService.downloadSinglePdf(billingPdf);
    } catch (error) {
      console.error("Error generating PDF:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      alert(`Failed to generate PDF: ${errorMessage}. Please try again.`);
    } finally {
      setIsGeneratingPdf(false);
    }
  };

  const preparePdfData = (roomPdf: RoomPdf) => {
    console.log("preparePdfData called with:", roomPdf);

    // Validate input data
    if (!roomPdf) {
      throw new Error("Room PDF data is null or undefined");
    }

    const billingPdf: BillingPdf = {
      buildingName: roomPdf.buildingName || "Unknown Building",
      customerName: roomPdf.customerName || "Unknown Customer",
      roomNumber: roomPdf.roomNumber || "Unknown Room",
      billingEnabledDate:
        roomPdf.billingEnabledDate || new Date().toISOString(),
      tableRows: [],
    };

    const rows = [];

    // Ensure all numeric values are valid
    const monthlyRent = Number(roomPdf.monthlyRent) || 0;
    const waterPricePerUnit = Number(roomPdf.waterPricePerUnit) || 0;
    const electricityPricePerUnit =
      Number(roomPdf.electricityPricePerUnit) || 0;
    const waterConsumption = Number(roomPdf.waterConsumption) || 0;
    const electricityConsumption = Number(roomPdf.electricityConsumption) || 0;
    const currentWaterReading = Number(roomPdf.currentWaterReading) || 0;
    const initialWaterReading = Number(roomPdf.initialWaterReading) || 0;
    const currentElectricityReading =
      Number(roomPdf.currentElectricityReading) || 0;
    const initialElectricityReading =
      Number(roomPdf.initialElectricityReading) || 0;

    rows.push({
      description: "ค่าห้อง",
      unit: "",
      amount: monthlyRent,
    });
    rows.push({
      description: `ค่าน้ำ หน่วยละ ${waterPricePerUnit} บาท ${currentWaterReading} - ${initialWaterReading}`,
      unit: waterConsumption.toString(),
      amount: waterConsumption * waterPricePerUnit,
    });
    rows.push({
      description: `ค่าไฟ หน่วยละ ${electricityPricePerUnit} บาท ${currentElectricityReading} - ${initialElectricityReading}`,
      unit: electricityConsumption.toString(),
      amount: electricityConsumption * electricityPricePerUnit,
    });
    roomPdf.additionalServices?.forEach((service) => {
      if (service) {
        rows.push({
          description: service.name,
          unit: "",
          amount: Number(service.price) || 0,
        });
      }
    });

    billingPdf.tableRows = rows;

    return billingPdf;
  };

  const handleBulkDownload = async () => {
    if (!building || billingHistory.length === 0) return;

    try {
      setIsBulkDownloading(true);
      setBulkProgress({ current: 0, total: billingHistory.length });

      const billingPdfs: BillingPdf[] = [];

      for (let i = 0; i < billingHistory.length; i++) {
        const entry = billingHistory[i];

        try {
          const room = await prepareRoomForPDF(entry);
          const billingPdf = preparePdfData(room);
          billingPdfs.push(billingPdf);

          setBulkProgress({ current: i + 1, total: billingHistory.length });
        } catch (error) {
          console.error(
            `Error processing entry for room ${entry.room_number}:`,
            error
          );
        }
      }

      if (billingPdfs.length > 0) {
        const zipFileName = PdfService.generateZipFileName(
          building.name,
          selectedMonth.toString(),
          selectedYear.toString()
        );

        await PdfService.downloadBulkPdfs(
          billingPdfs,
          zipFileName,
          (current, total) => setBulkProgress({ current, total })
        );
      } else {
        alert("No PDFs could be generated.");
      }
    } catch (error) {
      console.error("Error generating bulk PDFs:", error);
      alert("Failed to generate bulk PDFs. Please try again.");
    } finally {
      setIsBulkDownloading(false);
      setBulkProgress({ current: 0, total: 0 });
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push(`/buildings/${buildingId}`)}
                className="flex items-center space-x-2"
              >
                <ArrowLeft size={16} />
                <span>Building</span>
              </Button>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  {building?.name}
                </h1>
                <p className="text-sm text-gray-500">Building Management</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                Welcome, {user?.email}
              </span>
              {isAdmin && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  Admin
                </span>
              )}
              <Button onClick={signOut} variant="outline" size="sm">
                Sign Out
              </Button>
            </div>
          </div>
        </div>
      </header>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <p className="text-gray-600">
            View and manage billing entries and payments
          </p>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
          <div className="flex flex-wrap gap-4 items-end">
            <div>
              <label
                htmlFor="month"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Month
              </label>
              <select
                id="month"
                value={selectedMonth}
                onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                {months.map((month) => (
                  <option key={month.value} value={month.value}>
                    {month.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label
                htmlFor="year"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Year
              </label>
              <select
                id="year"
                value={selectedYear}
                onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                {years.map((year) => (
                  <option key={year} value={year}>
                    {year}
                  </option>
                ))}
              </select>
            </div>

            <button
              onClick={handleFilter}
              disabled={loading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
            >
              {loading ? "Loading..." : "Filter"}
            </button>

            {/* Bulk Download Button */}
            <button
              onClick={handleBulkDownload}
              disabled={isBulkDownloading || billingHistory.length === 0}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 flex items-center space-x-2"
            >
              <Download size={16} />
              <span>
                {isBulkDownloading
                  ? `Generating... (${bulkProgress.current}/${bulkProgress.total})`
                  : "Download All PDFs"}
              </span>
            </button>

            {/* Test PDF Button (for debugging) */}
            {/* <button
              onClick={() => PdfService.testPdfGeneration()}
              className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
            >
              Test PDF
            </button> */}
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <div className="mt-2 text-sm text-red-700">{error}</div>
              </div>
            </div>
          </div>
        )}

        {/* Billing History Table */}
        <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">
              Billing Entries
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              {billingHistory.length} entries found for{" "}
              {months.find((m) => m.value === filterMonth)?.label} {filterYear}
            </p>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Room Number
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Water Usage
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Electricity Usage
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Total Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Overdue Amount
                  </th>
                  {user?.role === "ADMIN" && (
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  )}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {loading ? (
                  <tr>
                    <td
                      colSpan={user?.role === "ADMIN" ? 8 : 7}
                      className="px-6 py-4 text-center text-gray-500"
                    >
                      Loading billing history...
                    </td>
                  </tr>
                ) : billingHistory.length === 0 ? (
                  <tr>
                    <td
                      colSpan={user?.role === "ADMIN" ? 8 : 7}
                      className="px-6 py-4 text-center text-gray-500"
                    >
                      No billing entries found for the selected period.
                    </td>
                  </tr>
                ) : (
                  billingHistory.map((entry) => (
                    <tr key={entry.id} className="hover:bg-gray-50">
                      <td
                        onClick={() => handleShowPaymentManagement(entry)}
                        className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 cursor-pointer hover:text-blue-400"
                      >
                        {entry.room_number}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(entry.created_at)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div>
                          <div>Initial: {entry.initial_water_reading}</div>
                          <div>Current: {entry.water_reading}</div>
                          <div className="font-medium">
                            Usage: {entry.water_consumption}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div>
                          <div>
                            Initial: {entry.initial_electricity_reading}
                          </div>
                          <div>Current: {entry.electricity_reading}</div>
                          <div className="font-medium">
                            Usage: {entry.electricity_consumption}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        ${entry.total_amount.toFixed(2)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={getStatusBadge(entry.status)}>
                          {entry.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        {entry.overDueAmount > 0 ? (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            ${entry.overDueAmount.toFixed(2)}
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            $0.00
                          </span>
                        )}
                      </td>
                      {user?.role === "ADMIN" && (
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex-col">
                            {(entry.status === "UNPAID" ||
                              entry.status === "PARTIALLY_PAID") && (
                              <div>
                                <button
                                  onClick={() => handleAddPayment(entry)}
                                  className="px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                                >
                                  Add Payment
                                </button>
                              </div>
                            )}
                            <div>
                              <button
                                onClick={() => handlePrint(entry)}
                                disabled={isGeneratingPdf}
                                className="mt-2 px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-orange-700 bg-orange-100 hover:bg-orange-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors duration-200 disabled:opacity-50 flex items-center space-x-1"
                              >
                                <FileText size={12} />
                                <span>
                                  {isGeneratingPdf ? "Generating..." : "Print"}
                                </span>
                              </button>
                            </div>
                          </div>
                        </td>
                      )}
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Payment Modal */}
        {showPaymentModal && selectedBillingEntry && (
          <PaymentModal
            billingEntry={selectedBillingEntry}
            onClose={() => {
              setShowPaymentModal(false);
              setSelectedBillingEntry(null);
              setReadOnly(false);
            }}
            onSuccess={handlePaymentSuccess}
            readOnly={readOnly}
          />
        )}
      </div>
    </div>
  );
}
