import { useState, useEffect } from "react";
import { BillingHistoryEntry, PaymentEntry } from "@/types";
import { billingAPI } from "@/lib/api";
import { paymentMethodsService } from "@/lib/services/payment-methods";
import {
  convertDecimalToNumber,
  processPaymentEntry,
  formatDate,
  getCurrentDateUTC7,
  toUTC7,
} from "@/lib/utils";

interface PaymentModalProps {
  billingEntry: BillingHistoryEntry;
  onClose: () => void;
  onSuccess: () => void;
  readOnly?: boolean; // New prop to control read-only mode
}

export default function PaymentModal({
  billingEntry,
  onClose,
  onSuccess,
  readOnly = false,
}: PaymentModalProps) {
  const [amount, setAmount] = useState<number>(0);
  const [paymentDate, setPaymentDate] = useState<string>(getCurrentDateUTC7());
  const [paymentMethodName, setPaymentMethodName] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [paymentHistory, setPaymentHistory] = useState<PaymentEntry[]>([]);
  const [loadingHistory, setLoadingHistory] = useState<boolean>(false);
  const [paymentMethods, setPaymentMethods] = useState<
    { id: string; name: string; description?: string }[]
  >([]);
  const [loadingPaymentMethods, setLoadingPaymentMethods] =
    useState<boolean>(false);

  const getStatusBadge = (status: string) => {
    const baseClasses =
      "px-3 py-1 rounded-full text-xs font-semibold uppercase tracking-wide";
    switch (status) {
      case "PAID":
        return `${baseClasses} bg-green-100 text-green-800 border border-green-200`;
      case "UNPAID":
        return `${baseClasses} bg-red-100 text-red-800 border border-red-200`;
      case "PARTIALLY_PAID":
        return `${baseClasses} bg-yellow-100 text-yellow-800 border border-yellow-200`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800 border border-gray-200`;
    }
  };

  // Calculate remaining balance - ensure values are numbers
  const totalAmount = convertDecimalToNumber(billingEntry.total_amount);
  const overDueAmount = convertDecimalToNumber(billingEntry.overDueAmount || 0);
  const remainingBalance = overDueAmount || totalAmount;

  useEffect(() => {
    // Set default amount to the remaining balance
    setAmount(remainingBalance);

    // Fetch payment methods
    const fetchPaymentMethods = async () => {
      setLoadingPaymentMethods(true);
      try {
        const { data, error } = await paymentMethodsService.getActive();
        if (error) {
          console.error("Error fetching payment methods:", error);
        } else if (data && data.length > 0) {
          setPaymentMethods(data);
          // Set default payment method to "Cash" if available, otherwise first method
          const defaultMethod =
            data.find((method) => method.name === "Cash") || data[0];
          setPaymentMethodName(defaultMethod.name);
        }
      } catch (err: any) {
        console.error("Error fetching payment methods:", err);
      } finally {
        setLoadingPaymentMethods(false);
      }
    };

    // Fetch payment history
    const fetchPaymentHistory = async () => {
      setLoadingHistory(true);
      try {
        const data = await billingAPI.getPaymentHistory(billingEntry.id);
        // Convert Decimal amounts to numbers
        const processedData = data.map(processPaymentEntry);
        setPaymentHistory(processedData);
      } catch (err: any) {
        console.error("Error fetching payment history:", err);
      } finally {
        setLoadingHistory(false);
      }
    };

    fetchPaymentMethods();
    fetchPaymentHistory();
  }, [billingEntry.id, remainingBalance]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    // Validate amount
    if (amount <= 0) {
      setError("Payment amount must be greater than 0");
      setLoading(false);
      return;
    }

    if (amount > remainingBalance) {
      setError(
        `Payment amount cannot exceed the remaining balance ($${remainingBalance.toFixed(
          2
        )})`
      );
      setLoading(false);
      return;
    }

    // Validate payment method
    if (!paymentMethodName) {
      setError("Please select a payment method");
      setLoading(false);
      return;
    }

    // Validate payment date (compare in UTC+7 timezone)
    const selectedDate = new Date(paymentDate);
    const today = new Date();
    console.log(selectedDate);
    if (selectedDate > today) {
      setError("Payment date cannot be in the future");
      setLoading(false);
      return;
    }

    try {
      await billingAPI.addPayment(billingEntry.id, {
        amount,
        paymentDate,
        paymentMethodName,
      });
      onSuccess();
    } catch (err: any) {
      setError(err.response?.data?.message || "Failed to add payment");
    } finally {
      setLoading(false);
    }
  };

  // Calculate total paid amount
  const totalPaid = paymentHistory.reduce(
    (sum, payment) => sum + payment.amount,
    0
  );

  return (
    <div className="fixed inset-0 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/80 transition-opacity z-20"
        onClick={onClose}
      />
      {/* Backdrop */}
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto m-4 z-50">
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold text-gray-900">
              Payment Management
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500 focus:outline-none"
            >
              <svg
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Billing Details
            </h3>
            <div className="bg-white border border-gray-200 p-4 rounded-md">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">
                    Room Number
                  </p>
                  <p className="font-semibold text-gray-900">
                    {billingEntry.room_number}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">
                    Billing Date
                  </p>
                  <p className="font-semibold text-gray-900">
                    {formatDate(billingEntry.created_at)}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">
                    Total Amount
                  </p>
                  <p className="font-semibold text-gray-900">
                    ${totalAmount.toFixed(2)}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">
                    Status
                  </p>
                  <span className={getStatusBadge(billingEntry.status)}>
                    {billingEntry.status}
                  </span>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">
                    Total Paid
                  </p>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-semibold bg-green-100 text-green-800">
                    ${totalPaid.toFixed(2)}
                  </span>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">
                    Remaining Balance
                  </p>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-semibold bg-red-100 text-red-800">
                    ${remainingBalance.toFixed(2)}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Payment History */}
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Payment History
            </h3>
            {loadingHistory ? (
              <p className="text-gray-500">Loading payment history...</p>
            ) : paymentHistory.length === 0 ? (
              <p className="text-gray-500">No payment records found.</p>
            ) : (
              <div className="overflow-x-auto border border-gray-200 rounded-md">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                        Amount
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                        Payment Method
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {paymentHistory.map((payment) => (
                      <tr key={payment.id} className="hover:bg-gray-50">
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                          {formatDate(payment.paymentDate)}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                          ${payment.amount.toFixed(2)}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {payment.paymentMethodName || "Cash"}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>

          {/* Add Payment Form - Only show for admin users in edit mode */}
          {!readOnly && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Add Payment
              </h3>
              <form onSubmit={handleSubmit}>
                {error && (
                  <div className="mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                    {error}
                  </div>
                )}

                <div className="mb-4">
                  <label
                    htmlFor="amount"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Amount ($)
                  </label>
                  <input
                    type="number"
                    id="amount"
                    value={amount}
                    onChange={(e) => setAmount(parseFloat(e.target.value) || 0)}
                    step="0.01"
                    min="0.01"
                    max={remainingBalance}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    Maximum payment: ${remainingBalance.toFixed(2)}
                  </p>
                </div>

                <div className="mb-4">
                  <label
                    htmlFor="paymentDate"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Payment Date
                  </label>
                  <input
                    type="date"
                    id="paymentDate"
                    value={paymentDate}
                    onChange={(e) => setPaymentDate(e.target.value)}
                    max={getCurrentDateUTC7()}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>

                <div className="mb-4">
                  <label
                    htmlFor="paymentMethod"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Payment Method *
                  </label>
                  {loadingPaymentMethods ? (
                    <div className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-500">
                      Loading payment methods...
                    </div>
                  ) : (
                    <select
                      id="paymentMethod"
                      value={paymentMethodName}
                      onChange={(e) => setPaymentMethodName(e.target.value)}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      required
                    >
                      <option value="">Select payment method</option>
                      {paymentMethods.map((method) => (
                        <option key={method.id} value={method.name}>
                          {method.name}
                          {method.description && ` - ${method.description}`}
                        </option>
                      ))}
                    </select>
                  )}
                </div>

                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={onClose}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={
                      loading ||
                      amount <= 0 ||
                      amount > remainingBalance ||
                      !paymentMethodName
                    }
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
                  >
                    {loading ? "Processing..." : "Add Payment"}
                  </button>
                </div>
              </form>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
