import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(amount);
}

// UTC+7 timezone constant
const UTC_PLUS_7_TIMEZONE = "Asia/Bangkok";

/**
 * Convert any date to UTC+7 timezone
 */
export function toUTC7(date: string | Date): Date {
  const inputDate = new Date(date);
  // Add 7 hours (25200000 milliseconds) to convert UTC to UTC+7
  return new Date(inputDate.getTime() + 7 * 60 * 60 * 1000);
}

/**
 * Format date in UTC+7 timezone
 */
export function formatDate(date: string | Date): string {
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    timeZone: UTC_PLUS_7_TIMEZONE,
  }).format(new Date(date));
}

/**
 * Format date Thai
 */
export function formatDateThai(date: string | Date): string {
  return new Intl.DateTimeFormat("th-TH", {
    year: "numeric",
    month: "short",
    day: "numeric",
    timeZone: UTC_PLUS_7_TIMEZONE,
  }).format(new Date(date));
}

/**
 * Format date and time TH in UTC+7 timezone
 */
export function formatDateTimeThai(date: string | Date): string {
  return new Intl.DateTimeFormat("th-TH", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    timeZone: UTC_PLUS_7_TIMEZONE,
  }).format(new Date(date));
}

/**
 * Format date and time in UTC+7 timezone
 */
export function formatDateTime(date: string | Date): string {
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    timeZone: UTC_PLUS_7_TIMEZONE,
  }).format(new Date(date));
}

/**
 * Format date for display in UTC+7 timezone (more readable format)
 */
export function formatDateDisplay(date: string | Date): string {
  return new Intl.DateTimeFormat("en-US", {
    weekday: "short",
    year: "numeric",
    month: "short",
    day: "numeric",
    timeZone: UTC_PLUS_7_TIMEZONE,
  }).format(new Date(date));
}

/**
 * Format date for input fields in UTC+7 timezone (YYYY-MM-DD format)
 */
export function formatDateForInput(date: string | Date = new Date()): string {
  const utc7Date = toUTC7(date);
  return utc7Date.toISOString().split("T")[0];
}

/**
 * Get current date in UTC+7 timezone formatted for input
 */
export function getCurrentDateUTC7(): string {
  return formatDateForInput(new Date());
}

/**
 * Format date with full details in UTC+7 timezone
 */
export function formatDateTimeFull(date: string | Date): string {
  return new Intl.DateTimeFormat("en-US", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    timeZone: UTC_PLUS_7_TIMEZONE,
    timeZoneName: "short",
  }).format(new Date(date));
}

export function calculateConsumption(
  currentReading: number,
  previousReading: number
): number {
  return Math.max(0, currentReading - previousReading);
}

export function calculateBillingAmount(
  waterConsumption: number,
  electricityConsumption: number,
  waterPricePerUnit: number,
  electricityPricePerUnit: number,
  additionalServicesCost: number = 0
): number {
  const waterCost = waterConsumption * waterPricePerUnit;
  const electricityCost = electricityConsumption * electricityPricePerUnit;
  return waterCost + electricityCost + additionalServicesCost;
}

/**
 * Converts Prisma Decimal values to JavaScript numbers
 * Handles both Decimal objects and string representations
 */
export function convertDecimalToNumber(value: any): number {
  if (value === null || value === undefined) {
    return 0;
  }

  // If it's already a number, return it
  if (typeof value === "number") {
    return value;
  }

  // If it's a Prisma Decimal object with toNumber method
  if (value && typeof value.toNumber === "function") {
    return value.toNumber();
  }

  // If it's a string or other type, convert to number
  return Number(value) || 0;
}

/**
 * Processes billing history entries to convert Decimal fields to numbers
 */
export function processBillingHistoryEntry(entry: any) {
  return {
    ...entry,
    initial_water_reading: convertDecimalToNumber(entry.initial_water_reading),
    initial_electricity_reading: convertDecimalToNumber(
      entry.initial_electricity_reading
    ),
    electricity_reading: convertDecimalToNumber(entry.electricity_reading),
    water_reading: convertDecimalToNumber(entry.water_reading),
    water_consumption: convertDecimalToNumber(entry.water_consumption),
    electricity_consumption: convertDecimalToNumber(
      entry.electricity_consumption
    ),
    total_amount: convertDecimalToNumber(entry.total_amount),
    overDueAmount: convertDecimalToNumber(entry.overDueAmount || 0),
  };
}

/**
 * Processes payment entries to convert Decimal amounts to numbers
 */
export function processPaymentEntry(payment: any) {
  return {
    ...payment,
    amount: convertDecimalToNumber(payment.amount),
  };
}

/**
 * Convert amount for PDF display
 */
export function numberToThaiBahtText(amount: number) {
  const tnum = [
    "ศูนย์",
    "หนึ่ง",
    "สอง",
    "สาม",
    "สี่",
    "ห้า",
    "หก",
    "เจ็ด",
    "แปด",
    "เก้า",
  ];
  const tunit = ["", "สิบ", "ร้อย", "พัน", "หมื่น", "แสน", "ล้าน"];

  const baht = Math.floor(amount);
  let bahtText = "";
  let bahtStr = baht.toString();
  let len = bahtStr.length;

  for (let i = 0; i < len; i++) {
    const num = parseInt(bahtStr[i]);
    if (num !== 0) {
      if (i === len - 1 && num === 1 && len > 1) {
        bahtText += "เอ็ด";
      } else if (i === len - 2 && num === 2) {
        bahtText += "ยี่";
      } else if (i === len - 2 && num === 1) {
        bahtText += "";
      } else {
        bahtText += tnum[num];
      }
      bahtText += tunit[len - i - 1];
    }
  }

  return bahtText + "บาทถ้วน";
}

export function numberToAmountNumberVM(amount: number) {
  return amount.toLocaleString("en-US", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
}
